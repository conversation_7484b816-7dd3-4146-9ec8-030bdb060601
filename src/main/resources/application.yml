spring:
  application:
    name: code-agent
  profiles:
    active: dev

# Agent??
agent:
  llm:
    provider: qwen
    api_key: ${QWEN_API_KEY:sk-4cae1961faad400c80dc7e1e058483ce}
    base_url: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
    model: qwen-plus-2025-07-28
    max-tokens: 32768
    timeout: 30s
    temperature: 0.2

  tools:
    file-search:
      max-results: 20
      supported-extensions: [.java, .yml, .xml, .properties, .md, .sh, .txt]
      exclude-dirs:
        - target
        - git
        - .idea
        - node_modules

    code-analysis:
      max-complexity: 10
      max-line-length: 120

    cache:
      enable: true
      max-size: 1000
      expire-after-write: 30m

logging:
  level:
    life.ljs.codeagent: INFO
    org.springframework: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics

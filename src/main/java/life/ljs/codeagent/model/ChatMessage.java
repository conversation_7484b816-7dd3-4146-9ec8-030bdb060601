package life.ljs.codeagent.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/8/21
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChatMessage {
    private String role;
    private String content;

    @JsonProperty("tool_calls")
    private Object toolCalls;

    @JsonProperty("tool_call_id")
    private String toolCallId;
}

package life.ljs.codeagent.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import life.ljs.codeagent.config.AgentConfig;
import life.ljs.codeagent.model.ChatMessage;
import life.ljs.codeagent.model.ToolDefinition;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/8/21
 **/
@Slf4j
@Service
public class QwenLLMService {
    private final AgentConfig agentConfig;
    private final ObjectMapper objectMapper;
    private final OkHttpClient okHttpClient;

    public QwenLLMService(AgentConfig agentConfig, ObjectMapper objectMapper){
        this.agentConfig = agentConfig;
        this.objectMapper = objectMapper;
        this.okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(Duration.ofSeconds(10))
                .readTimeout(agentConfig.getLlm().getTimeout())
                .build();
    }

    public JsonNode callLLM(List<ChatMessage> messages, List<ToolDefinition> tools) throws IOException {
        Map<String,Object> payload = buildPayload(messages,tools);
        String jsonBody = objectMapper.writeValueAsString(payload);
        RequestBody requestBody = RequestBody.create(jsonBody, MediaType.get("application/json"));
        Request request = new Request.Builder()
                .url(agentConfig.getLlm().getBaseUrl())
                .addHeader("Authorization", "Bearer " + agentConfig.getLlm().getApiKey())
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build();

        try (okhttp3.Response response = okHttpClient.newCall(request).execute()) {
            if(!response.isSuccessful()){
                String errorBody = response.body() != null ? response.body().string() : "No response body";
                log.error("API call failed: {} - {}\nrequest body:{}", response.code(), errorBody, jsonBody);
                throw new IOException("LLM API call failed: " + response.code() + " " + response.message());
            }
            String responseBody = response.body().string();
            log.debug("LLM response: {}", responseBody);
            return objectMapper.readTree(responseBody);
        }
    }

    private Map<String, Object> buildPayload(List<ChatMessage> messages, List<ToolDefinition> tools) {
        Map<String,Object> payload = new HashMap<>();
        payload.put("model", agentConfig.getLlm().getModel());
        // 消息列表（直接使用，不需要包装在input中）
        payload.put("messages", messages);

        // 工具定义（如果存在）
        if (tools != null && !tools.isEmpty()) {
            payload.put("tools", tools);
            payload.put("tool_choice", "auto"); // 自动选择是否调用工具
        }

        // 生成参数
        payload.put("temperature", agentConfig.getLlm().getTemperature());
        payload.put("max_tokens", agentConfig.getLlm().getMaxTokens());

        // 其他可选参数
        payload.put("stream", false); // 不使用流式输出

        return payload;
    }
}

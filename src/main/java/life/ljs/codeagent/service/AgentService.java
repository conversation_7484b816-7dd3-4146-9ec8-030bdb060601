package life.ljs.codeagent.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import life.ljs.codeagent.model.ChatMessage;
import life.ljs.codeagent.model.ToolDefinition;
import life.ljs.codeagent.tools.BaseTool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Agent服务类 - 协调LLM和工具调用
 * 
 * <AUTHOR> carp
 * @version 1.0.0
 * @since 2025/8/22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentService {
    
    private final QwenLLMService qwenLLMService;
    private final List<BaseTool> tools;
    private final ObjectMapper objectMapper;
    
    /**
     * 处理用户消息并返回Agent响应
     */
    public String processMessage(String userMessage) {
        try {
            // 构建消息列表
            List<ChatMessage> messages = new ArrayList<>();
            messages.add(ChatMessage.builder()
                    .role("user")
                    .content(userMessage)
                    .build());
            
            // 获取工具定义
            List<ToolDefinition> toolDefinitions = tools.stream()
                    .map(BaseTool::toDefinition)
                    .collect(Collectors.toList());
            
            // 调用LLM
            JsonNode response = qwenLLMService.callLLM(messages, toolDefinitions);
            
            // 处理响应
            return processLLMResponse(response, messages);
            
        } catch (Exception e) {
            log.error("处理消息失败: {}", userMessage, e);
            return "抱歉，处理您的请求时出现了错误: " + e.getMessage();
        }
    }
    
    /**
     * 处理LLM响应，包括工具调用
     */
    private String processLLMResponse(JsonNode response, List<ChatMessage> messages) throws Exception {
        JsonNode choices = response.get("choices");
        if (choices == null || choices.isEmpty()) {
            return "LLM没有返回有效响应";
        }
        
        JsonNode choice = choices.get(0);
        JsonNode message = choice.get("message");
        
        String content = message.has("content") && !message.get("content").isNull() 
                ? message.get("content").asText() 
                : "";
        
        // 检查是否有工具调用
        if (message.has("tool_calls") && !message.get("tool_calls").isNull()) {
            JsonNode toolCalls = message.get("tool_calls");
            
            // 添加助手消息到对话历史
            messages.add(ChatMessage.builder()
                    .role("assistant")
                    .content(content)
                    .toolCalls(toolCalls)
                    .build());
            
            // 处理工具调用
            for (JsonNode toolCall : toolCalls) {
                String toolCallResult = executeToolCall(toolCall);
                
                // 添加工具调用结果到消息历史
                messages.add(ChatMessage.builder()
                        .role("tool")
                        .content(toolCallResult)
                        .build());
            }
            
            // 再次调用LLM获取最终响应
            List<ToolDefinition> toolDefinitions = tools.stream()
                    .map(BaseTool::toDefinition)
                    .collect(Collectors.toList());
            
            JsonNode finalResponse = qwenLLMService.callLLM(messages, toolDefinitions);
            return extractContentFromResponse(finalResponse);
        }
        
        return content;
    }
    
    /**
     * 执行工具调用
     */
    private String executeToolCall(JsonNode toolCall) {
        try {
            JsonNode function = toolCall.get("function");
            String functionName = function.get("name").asText();
            String argumentsStr = function.get("arguments").asText();
            
            log.info("执行工具调用: {} with arguments: {}", functionName, argumentsStr);
            
            // 解析参数
            Map<String, Object> arguments = objectMapper.readValue(argumentsStr, Map.class);
            
            // 查找对应的工具
            BaseTool tool = tools.stream()
                    .filter(t -> t.getName().equals(functionName))
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("未找到工具: " + functionName));
            
            // 执行工具
            String result = tool.execute(arguments);
            log.info("工具执行结果: {}", result);
            
            return result;
            
        } catch (Exception e) {
            log.error("工具调用执行失败", e);
            return "工具调用执行失败: " + e.getMessage();
        }
    }
    
    /**
     * 从LLM响应中提取内容
     */
    private String extractContentFromResponse(JsonNode response) {
        try {
            JsonNode choices = response.get("choices");
            if (choices != null && !choices.isEmpty()) {
                JsonNode choice = choices.get(0);
                JsonNode message = choice.get("message");
                if (message.has("content") && !message.get("content").isNull()) {
                    return message.get("content").asText();
                }
            }
            return "LLM没有返回有效内容";
        } catch (Exception e) {
            log.error("提取响应内容失败", e);
            return "提取响应内容失败: " + e.getMessage();
        }
    }
    
    /**
     * 获取可用工具列表
     */
    public List<String> getAvailableTools() {
        return tools.stream()
                .map(BaseTool::getName)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取工具描述
     */
    public Map<String, String> getToolDescriptions() {
        return tools.stream()
                .collect(Collectors.toMap(
                        BaseTool::getName,
                        BaseTool::getDescription
                ));
    }
}

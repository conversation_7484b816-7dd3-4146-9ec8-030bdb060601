package life.ljs.codeagent.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import life.ljs.codeagent.model.ChatMessage;
import life.ljs.codeagent.model.ToolDefinition;
import life.ljs.codeagent.tools.BaseTool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Agent服务类 - 协调LLM和工具调用
 * 
 * <AUTHOR> carp
 * @version 1.0.0
 * @since 2025/8/22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentService {
    
    private final QwenLLMService qwenLLMService;
    private final List<BaseTool> tools;
    private final ObjectMapper objectMapper;

    private List<ChatMessage> conversationHistory = new ArrayList<>();
    
    /**
     * 处理用户消息并返回Agent响应
     */
    public String processMessage(String userMessage) {
        try {
            // 添加用户消息到历史
            conversationHistory.add(ChatMessage.builder()
                    .role("user")
                    .content(userMessage)
                    .build());
            // 构建消息列表
            List<ChatMessage> messages = buildMessages();
            // 获取工具定义
            List<ToolDefinition> toolDefinitions = tools.stream()
                    .map(BaseTool::toDefinition)
                    .collect(Collectors.toList());
            
            // 调用LLM
            JsonNode response = qwenLLMService.callLLM(messages, toolDefinitions);
            
            // 处理响应
            return processLLMResponse(response, messages);
            
        } catch (Exception e) {
            log.error("处理消息失败: {}", userMessage, e);
            return "抱歉，处理您的请求时出现了错误: " + e.getMessage();
        }
    }

    private List<ChatMessage> buildMessages() {
        List<ChatMessage> messages = new ArrayList<>();
        // 系统提示词
        messages.add(ChatMessage.builder()
                .role("system")
                .content("""
                        你是一个智能代码助手Agent，专门帮助开发者分析和管理Java项目。你可以：
                        1. 🔍 搜索项目文件和代码内容
                        2. 📊 分析Java代码质量和复杂度
                        3. 🔧 执行Git操作（状态查看、历史记录等）
                        4. 🌳 生成项目结构树
                        5. 📝 收集TODO和FIXME注释
                        请根据用户需求，选择合适的工具来完成任务。如果需要多个步骤，请逐步执行。
                        始终用中文回复，并提供清晰、有用的分析结果。
                        """)
                .build());
        // 添加对话历史（保持最近20条）
        int startIndex = Math.max(0, conversationHistory.size() - 20);
        messages.addAll(conversationHistory.subList(startIndex, conversationHistory.size()));
        return messages;
    }

    /**
     * 处理LLM响应，包括工具调用
     */
    private String processLLMResponse(JsonNode response, List<ChatMessage> messages) throws Exception {
        JsonNode choices = response.get("choices");
        if (choices == null || choices.isEmpty()) {
            return "LLM没有返回有效响应";
        }

        JsonNode choice = choices.get(0);
        JsonNode message = choice.get("message");

        // 检查是否有工具调用
        if (message.has("tool_calls") && !message.get("tool_calls").isNull()) {
            return handleCallTools( message.get("tool_calls"));
        }else{
            // 直接文本回复
            String content = message.has("content") && !message.get("content").isNull()
                    ? message.get("content").asText()
                    : "";
            if(content.isEmpty()){
                log.warn("Empty content in response: {}", message);
                return "LLM没有返回有效内容";
            }else{
                // 添加助手消息到对话历史
                conversationHistory.add(ChatMessage.builder()
                        .role("assistant")
                        .content(content)
                        .build());
                return content;
            }
        }

    }

    private String handleCallTools(JsonNode toolCalls) throws IOException {
        // 1. 记录assistant的工具调用到历史（非常重要！tool消息必须跟在这之后）
        ChatMessage toolCallMessage = ChatMessage.builder()
                .role("assistant")
                .content("") // OpenAI格式中，有tool_calls时content可以为空
                .toolCalls(toolCalls) // 保存完整的tool_calls信息
                .build();
        conversationHistory.add(toolCallMessage);
        List<ChatMessage> messages = buildMessages();
        // 处理工具调用
        for (JsonNode toolCall : toolCalls) {
            String toolCallId = toolCall.get("id").asText();
            String toolCallResult = executeToolCall(toolCall);

            // 添加工具调用结果到消息历史，必须包含tool_call_id
            ChatMessage toolMessage = ChatMessage.builder()
                    .role("tool")
                    .content(toolCallResult)
                    .build();

            // 设置tool_call_id（需要在ChatMessage中添加这个字段）
            toolMessage.setToolCallId(toolCallId);
            messages.add(toolMessage);
        }
        // 添加特殊的system提示，指导LLM如何处理工具结果
        ChatMessage toolResultSystemMessage = ChatMessage.builder()
                .role("system")
                .content("""
                        现在请基于上述工具执行结果，为用户生成一个智能、友好的回答。要求：
                        1. 用中文回复，语言自然流畅
                        2. 整合所有工具结果，提供综合分析
                        3. 重点突出关键发现和问题
                        4. 提供具体可行的改进建议
                        5. 可以引用具体的数据和细节
                        6. 如果发现问题，要说明影响和解决方案
                        """)
                .build();
        messages.add(toolResultSystemMessage);
        JsonNode finalResponse = qwenLLMService.callLLM(messages, null);
        return extractContentFromResponse(finalResponse);
    }

    /**
     * 执行工具调用
     */
    private String executeToolCall(JsonNode toolCall) {
        try {
            JsonNode function = toolCall.get("function");
            String functionName = function.get("name").asText();
            String argumentsStr = function.get("arguments").asText();
            
            log.info("执行工具调用: {} with arguments: {}", functionName, argumentsStr);
            
            // 解析参数
            Map<String, Object> arguments = objectMapper.readValue(argumentsStr, Map.class);
            
            // 查找对应的工具
            BaseTool tool = tools.stream()
                    .filter(t -> t.getName().equals(functionName))
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("未找到工具: " + functionName));
            
            // 执行工具
            String result = tool.execute(arguments);
            log.info("工具执行结果: {}", result);
            
            return result;
            
        } catch (Exception e) {
            log.error("工具调用执行失败", e);
            return "工具调用执行失败: " + e.getMessage();
        }
    }
    
    /**
     * 从LLM响应中提取内容
     */
    private String extractContentFromResponse(JsonNode response) {
        try {
            JsonNode choices = response.get("choices");
            if (choices != null && !choices.isEmpty()) {
                JsonNode choice = choices.get(0);
                JsonNode message = choice.get("message");
                if (message.has("content") && !message.get("content").isNull()) {
                    return message.get("content").asText();
                }
            }
            return "LLM没有返回有效内容";
        } catch (Exception e) {
            log.error("提取响应内容失败", e);
            return "提取响应内容失败: " + e.getMessage();
        }
    }
    
    /**
     * 获取可用工具列表
     */
    public List<String> getAvailableTools() {
        return tools.stream()
                .map(BaseTool::getName)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取工具描述
     */
    public Map<String, String> getToolDescriptions() {
        return tools.stream()
                .collect(Collectors.toMap(
                        BaseTool::getName,
                        BaseTool::getDescription
                ));
    }
}

package life.ljs.codeagent.tools;

import life.ljs.codeagent.model.ToolDefinition;

import java.util.Map;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/8/21
 **/
public interface BaseTool {
    String getName();
    String getDescription();
    Map<String, Object> getParameters();
    String execute(Map<String, Object> arguments) throws Exception;

    default ToolDefinition toDefinition() {
        return ToolDefinition.builder()
                .type("function")
                .function(ToolDefinition.FunctionDefinition.builder()
                        .name(getName())
                        .description(getDescription())
                        .parameters(getParameters())
                        .build())
                .build();
    }
}

package life.ljs.codeagent.tools.impl;

import life.ljs.codeagent.config.AgentConfig;
import life.ljs.codeagent.tools.BaseTool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.naming.directory.SearchResult;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Stream;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/8/21
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class FileSearchTool implements BaseTool {
    private final AgentConfig agentConfig;

    @Override
    public String getName() {
        return "file_search";
    }

    @Override
    public String getDescription() {
        return "在项目中搜索文件或文件内容，支持文件名和内容搜索";
    }

    @Override
    public Map<String, Object> getParameters() {
        Map<String, Object> properties = new HashMap<>();
        properties.put("query", Map.of("type", "string", "description", "搜索关键词"));
        properties.put("searchType", Map.of(
                "type", "string",
                "enum", Arrays.asList("filename", "content", "both"),
                "description", "搜索类型: filename(文件名), content(文件内容), both(两者)"
        ));
        properties.put("fileExtensions", Map.of(
                "type", "array",
                "items", Map.of("type", "string"),
                "description", "文件扩展名过滤，默认支持："+agentConfig.getTools().getFileSearch().getSupportedExtensions()
        ));

        return Map.of(
                "type", "object",
                "properties", properties,
                "required", Arrays.asList("query")
        );
    }

    @Override
    @Cacheable(value = "fileSearchCache", key = "#arguments.toString()")
    public String execute(Map<String, Object> arguments) throws Exception {
        String query = (String) arguments.get("query");
        String searchType = (String) arguments.getOrDefault("searchType", "both");
        @SuppressWarnings("unchecked")
        List<String> fileExtensions = (List<String>) arguments.get("fileExtensions");

        if (fileExtensions == null) {
            fileExtensions = agentConfig.getTools().getFileSearch().getSupportedExtensions();
        }

        SearchResult result = performSearch(query, searchType, fileExtensions);
        return formatSearchResult(result, query);
    }

    private String formatSearchResult(SearchResult result, String query) {
        StringBuilder sb = new StringBuilder();

        if (result.getFilenameMatches().isEmpty() && result.getContentMatches().isEmpty()) {
            return String.format("未找到包含 '%s' 的文件或内容", query);
        }

        if (!result.getFilenameMatches().isEmpty()) {
            sb.append("📁 文件名匹配:\n");
            result.getFilenameMatches().forEach(file ->
                    sb.append("  ").append(file).append("\n"));
        }

        if (!result.getContentMatches().isEmpty()) {
            if (sb.length() > 0) sb.append("\n");
            sb.append("📄 内容匹配:\n");
            result.getContentMatches().forEach(match ->
                    sb.append(String.format("  %s 第%d行: %s\n",
                            match.getFile(), match.getLineNumber(), match.getContent())));
        }

        return sb.toString();
    }

    private SearchResult performSearch(String query, String searchType, List<String> fileExtensions) throws IOException {
        SearchResult result = new SearchResult();
        Path projectRoot = Paths.get(System.getProperty("user.dir"));
        Set<String> excludeDirs = new HashSet<>(agentConfig.getTools().getFileSearch().getExcludeDirs());

        try (Stream<Path> paths = Files.walk(projectRoot)) {
            paths.filter(Files::isRegularFile)
                    .filter(path -> shouldIncludeFile(path, fileExtensions, excludeDirs))
                    .limit(1000) // 防止处理过多文件
                    .forEach(path -> {
                        try {
                            processFile(path, query, searchType, result, projectRoot);
                        } catch (IOException e) {
                            log.warn("处理文件失败: {}", path, e);
                        }
                    });
        }

        return result;
    }

    private void processFile(Path filePath, String query, String searchType, SearchResult result, Path projectRoot) throws IOException {
        String relativePath = projectRoot.relativize(filePath).toString();

        // 文件名搜索
        if ("filename".equals(searchType) || "both".equals(searchType)) {
            if (filePath.getFileName().toString().toLowerCase()
                    .contains(query.toLowerCase())) {
                result.addFilenameMatch(relativePath);
            }
        }

        // 文件内容搜索
        if ("content".equals(searchType) || "both".equals(searchType)) {
            List<String> lines = Files.readAllLines(filePath);
            for (int i = 0; i < lines.size(); i++) {
                String line = lines.get(i);
                if (line.toLowerCase().contains(query.toLowerCase())) {
                    result.addContentMatch(relativePath, i + 1, line.trim());
                    if (result.getContentMatches().size() >= agentConfig.getTools().getFileSearch().getMaxResults()) {
                        return; // 达到最大结果数限制
                    }
                }
            }
        }
    }

    private boolean shouldIncludeFile(Path filePath, List<String> fileExtensions, Set<String> excludeDirs) {
        // 检查是否在排除目录中
        for (Path part : filePath) {
            if (excludeDirs.contains(part.toString())) {
                return false;
            }
        }

        // 检查文件扩展名
        String fileName = filePath.getFileName().toString();
        return fileExtensions.stream().anyMatch(ext -> fileName.toLowerCase().endsWith(ext.toLowerCase()));
    }

    // 内部数据结构
    private static class SearchResult {
        private final List<String> filenameMatches = new ArrayList<>();
        private final List<ContentMatch> contentMatches = new ArrayList<>();

        public void addFilenameMatch(String file) {
            filenameMatches.add(file);
        }

        public void addContentMatch(String file, int lineNumber, String content) {
            contentMatches.add(new ContentMatch(file, lineNumber, content));
        }

        public List<String> getFilenameMatches() { return filenameMatches; }
        public List<ContentMatch> getContentMatches() { return contentMatches; }
    }

    private static class ContentMatch {
        private final String file;
        private final int lineNumber;
        private final String content;

        public ContentMatch(String file, int lineNumber, String content) {
            this.file = file;
            this.lineNumber = lineNumber;
            this.content = content;
        }

        public String getFile() { return file; }
        public int getLineNumber() { return lineNumber; }
        public String getContent() { return content; }
    }
}

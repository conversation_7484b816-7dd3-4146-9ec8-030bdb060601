package life.ljs.codeagent.controller;

import life.ljs.codeagent.service.AgentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Agent控制器 - 提供HTTP接口
 * 
 * <AUTHOR> carp
 * @version 1.0.0
 * @since 2025/8/22
 */
@Slf4j
@RestController
@RequestMapping("/api/agent")
@RequiredArgsConstructor
public class AgentController {
    
    private final AgentService agentService;
    
    /**
     * 处理用户消息
     */
    @PostMapping("/chat")
    public Map<String, Object> chat(@RequestBody Map<String, String> request) {
        try {
            String message = request.get("message");
            if (message == null || message.trim().isEmpty()) {
                return Map.of(
                    "success", false,
                    "error", "消息不能为空"
                );
            }
            
            log.info("收到用户消息: {}", message);
            String response = agentService.processMessage(message);
            log.info("Agent响应: {}", response);
            
            return Map.of(
                "success", true,
                "response", response
            );
            
        } catch (Exception e) {
            log.error("处理聊天请求失败", e);
            return Map.of(
                "success", false,
                "error", "处理请求时出现错误: " + e.getMessage()
            );
        }
    }
    
    /**
     * 获取可用工具列表
     */
    @GetMapping("/tools")
    public Map<String, Object> getTools() {
        try {
            List<String> tools = agentService.getAvailableTools();
            Map<String, String> descriptions = agentService.getToolDescriptions();
            
            return Map.of(
                "success", true,
                "tools", tools,
                "descriptions", descriptions
            );
            
        } catch (Exception e) {
            log.error("获取工具列表失败", e);
            return Map.of(
                "success", false,
                "error", "获取工具列表失败: " + e.getMessage()
            );
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        return Map.of(
            "status", "ok",
            "timestamp", System.currentTimeMillis()
        );
    }
}

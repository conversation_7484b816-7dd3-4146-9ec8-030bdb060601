package life.ljs.codeagent.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import life.ljs.codeagent.model.ChatMessage;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工具调用修复测试
 * 验证修复后的工具调用消息格式
 * 
 * <AUTHOR> carp
 * @version 1.0.0
 * @since 2025/8/22
 */
@SpringBootTest
class ToolCallFixTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testChatMessageSerialization() throws Exception {
        // 测试用户消息
        ChatMessage userMessage = ChatMessage.builder()
                .role("user")
                .content("帮我搜索文件")
                .build();
        
        String userJson = objectMapper.writeValueAsString(userMessage);
        System.out.println("用户消息JSON: " + userJson);
        
        // 验证用户消息不包含tool相关字段
        assertFalse(userJson.contains("tool_calls"));
        assertFalse(userJson.contains("tool_call_id"));
        
        // 测试助手消息（带工具调用）
        String toolCallsJson = """
            [{
                "id": "call_123",
                "type": "function",
                "function": {
                    "name": "file_search",
                    "arguments": "{\\"query\\": \\"test\\"}"
                }
            }]
            """;
        
        JsonNode toolCallsNode = objectMapper.readTree(toolCallsJson);
        
        ChatMessage assistantMessage = ChatMessage.builder()
                .role("assistant")
                .content("我来帮您搜索文件")
                .toolCalls(toolCallsNode)
                .build();
        
        String assistantJson = objectMapper.writeValueAsString(assistantMessage);
        System.out.println("助手消息JSON: " + assistantJson);
        
        // 验证助手消息包含tool_calls
        assertTrue(assistantJson.contains("tool_calls"));
        assertFalse(assistantJson.contains("tool_call_id"));
        
        // 测试工具结果消息
        ChatMessage toolMessage = ChatMessage.builder()
                .role("tool")
                .content("搜索结果：找到3个文件")
                .toolCallId("call_123")
                .build();
        
        String toolJson = objectMapper.writeValueAsString(toolMessage);
        System.out.println("工具消息JSON: " + toolJson);
        
        // 验证工具消息包含tool_call_id
        assertTrue(toolJson.contains("tool_call_id"));
        assertTrue(toolJson.contains("call_123"));
        assertFalse(toolJson.contains("tool_calls"));
    }

    @Test
    void testToolCallMessageFlow() throws Exception {
        // 模拟完整的工具调用消息流
        List<ChatMessage> messages = new ArrayList<>();
        
        // 1. 用户消息
        messages.add(ChatMessage.builder()
                .role("user")
                .content("搜索Java文件")
                .build());
        
        // 2. 助手消息（带工具调用）
        String toolCallJson = """
            [{
                "id": "call_abc123",
                "type": "function",
                "function": {
                    "name": "file_search",
                    "arguments": "{\\"query\\": \\"java\\", \\"searchType\\": \\"filename\\"}"
                }
            }]
            """;
        
        JsonNode toolCallNode = objectMapper.readTree(toolCallJson);
        messages.add(ChatMessage.builder()
                .role("assistant")
                .content("我来为您搜索Java文件")
                .toolCalls(toolCallNode)
                .build());
        
        // 3. 工具结果消息
        messages.add(ChatMessage.builder()
                .role("tool")
                .content("找到以下Java文件：\n- FileSearchTool.java\n- AgentService.java")
                .toolCallId("call_abc123")
                .build());
        
        // 4. 最终助手响应
        messages.add(ChatMessage.builder()
                .role("assistant")
                .content("我为您找到了2个Java文件：FileSearchTool.java和AgentService.java")
                .build());
        
        // 验证消息序列化
        String messagesJson = objectMapper.writeValueAsString(messages);
        System.out.println("完整消息流JSON: " + messagesJson);
        
        // 验证JSON结构正确
        JsonNode messagesNode = objectMapper.readTree(messagesJson);
        assertEquals(4, messagesNode.size());
        
        // 验证第一条消息（用户）
        JsonNode userMsg = messagesNode.get(0);
        assertEquals("user", userMsg.get("role").asText());
        assertFalse(userMsg.has("tool_calls"));
        assertFalse(userMsg.has("tool_call_id"));
        
        // 验证第二条消息（助手带工具调用）
        JsonNode assistantMsg = messagesNode.get(1);
        assertEquals("assistant", assistantMsg.get("role").asText());
        assertTrue(assistantMsg.has("tool_calls"));
        assertFalse(assistantMsg.has("tool_call_id"));
        
        // 验证第三条消息（工具结果）
        JsonNode toolMsg = messagesNode.get(2);
        assertEquals("tool", toolMsg.get("role").asText());
        assertTrue(toolMsg.has("tool_call_id"));
        assertEquals("call_abc123", toolMsg.get("tool_call_id").asText());
        assertFalse(toolMsg.has("tool_calls"));
        
        // 验证第四条消息（最终响应）
        JsonNode finalMsg = messagesNode.get(3);
        assertEquals("assistant", finalMsg.get("role").asText());
        assertFalse(finalMsg.has("tool_calls"));
        assertFalse(finalMsg.has("tool_call_id"));
    }

    @Test
    void testToolCallIdExtraction() throws Exception {
        // 测试从工具调用中提取ID
        String toolCallJson = """
            {
                "id": "call_xyz789",
                "type": "function",
                "function": {
                    "name": "file_search",
                    "arguments": "{\\"query\\": \\"test\\"}"
                }
            }
            """;
        
        JsonNode toolCall = objectMapper.readTree(toolCallJson);
        
        // 验证可以正确提取ID
        assertTrue(toolCall.has("id"));
        String toolCallId = toolCall.get("id").asText();
        assertEquals("call_xyz789", toolCallId);
        
        // 验证可以提取函数信息
        JsonNode function = toolCall.get("function");
        assertEquals("file_search", function.get("name").asText());
        
        String arguments = function.get("arguments").asText();
        assertTrue(arguments.contains("test"));
    }

    @Test
    void testMessageValidation() {
        // 测试消息验证逻辑
        
        // 有效的用户消息
        ChatMessage validUser = ChatMessage.builder()
                .role("user")
                .content("Hello")
                .build();
        assertNotNull(validUser.getRole());
        assertNotNull(validUser.getContent());
        assertNull(validUser.getToolCalls());
        assertNull(validUser.getToolCallId());
        
        // 有效的工具消息
        ChatMessage validTool = ChatMessage.builder()
                .role("tool")
                .content("Tool result")
                .toolCallId("call_123")
                .build();
        assertEquals("tool", validTool.getRole());
        assertNotNull(validTool.getToolCallId());
        assertNull(validTool.getToolCalls());
        
        // 有效的助手消息（带工具调用）
        ChatMessage validAssistant = ChatMessage.builder()
                .role("assistant")
                .content("Calling tool")
                .toolCalls("mock_tool_calls")
                .build();
        assertEquals("assistant", validAssistant.getRole());
        assertNotNull(validAssistant.getToolCalls());
        assertNull(validAssistant.getToolCallId());
    }
}

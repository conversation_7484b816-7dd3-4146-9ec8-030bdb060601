package life.ljs.codeagent.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import life.ljs.codeagent.model.ChatMessage;
import life.ljs.codeagent.model.ToolDefinition;
import life.ljs.codeagent.tools.impl.FileSearchTool;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 端到端工具调用测试
 * 验证完整的工具调用流程和消息格式
 * 
 * <AUTHOR> carp
 * @version 1.0.0
 * @since 2025/8/22
 */
@SpringBootTest
class EndToEndToolCallTest {

    @Autowired
    private FileSearchTool fileSearchTool;
    
    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testCompleteToolCallWorkflow() throws Exception {
        System.out.println("=== 完整工具调用流程测试 ===");
        
        // 1. 准备工具定义
        ToolDefinition toolDef = fileSearchTool.toDefinition();

        System.out.println("工具定义: " + objectMapper.writeValueAsString(toolDef));
        
        // 2. 构建初始消息
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(ChatMessage.builder()
                .role("user")
                .content("请帮我搜索项目中包含'Agent'的Java文件")
                .build());
        
        System.out.println("初始消息: " + objectMapper.writeValueAsString(messages));
        
        // 3. 模拟LLM返回工具调用
        String mockLLMResponse = """
            {
                "choices": [{
                    "message": {
                        "role": "assistant",
                        "content": "我来帮您搜索包含'Agent'的Java文件",
                        "tool_calls": [{
                            "id": "call_search_001",
                            "type": "function",
                            "function": {
                                "name": "file_search",
                                "arguments": "{\\"query\\": \\"Agent\\", \\"searchType\\": \\"both\\", \\"fileExtensions\\": [\\".java\\"]}"
                            }
                        }]
                    }
                }]
            }
            """;
        
        JsonNode llmResponse = objectMapper.readTree(mockLLMResponse);
        System.out.println("模拟LLM响应: " + mockLLMResponse);
        
        // 4. 处理LLM响应，添加助手消息
        JsonNode choice = llmResponse.get("choices").get(0);
        JsonNode message = choice.get("message");
        
        ChatMessage assistantMessage = ChatMessage.builder()
                .role("assistant")
                .content(message.get("content").asText())
                .toolCalls(message.get("tool_calls"))
                .build();
        
        messages.add(assistantMessage);
        System.out.println("添加助手消息后: " + objectMapper.writeValueAsString(messages));
        
        // 5. 执行工具调用
        JsonNode toolCalls = message.get("tool_calls");
        for (JsonNode toolCall : toolCalls) {
            String toolCallId = toolCall.get("id").asText();
            JsonNode function = toolCall.get("function");
            String functionName = function.get("name").asText();
            String argumentsStr = function.get("arguments").asText();
            
            System.out.println("执行工具调用:");
            System.out.println("  ID: " + toolCallId);
            System.out.println("  函数: " + functionName);
            System.out.println("  参数: " + argumentsStr);
            
            // 解析参数并执行工具
            @SuppressWarnings("unchecked")
            Map<String, Object> arguments = objectMapper.readValue(argumentsStr, Map.class);
            String toolResult = fileSearchTool.execute(arguments);
            
            System.out.println("  结果: " + toolResult);
            
            // 添加工具结果消息
            ChatMessage toolMessage = ChatMessage.builder()
                    .role("tool")
                    .content(toolResult)
                    .toolCallId(toolCallId)
                    .build();
            
            messages.add(toolMessage);
        }
        
        System.out.println("添加工具结果后: " + objectMapper.writeValueAsString(messages));
        
        // 6. 验证消息格式正确性
        validateMessageFormat(messages);
        
        // 7. 模拟最终LLM响应
        ChatMessage finalResponse = ChatMessage.builder()
                .role("assistant")
                .content("根据搜索结果，我为您找到了包含'Agent'的Java文件。这些文件是项目的核心组件。")
                .build();
        
        messages.add(finalResponse);
        
        System.out.println("最终消息流: " + objectMapper.writeValueAsString(messages));
        
        // 验证完整流程
        assertEquals(4, messages.size());
        assertEquals("user", messages.get(0).getRole());
        assertEquals("assistant", messages.get(1).getRole());
        assertEquals("tool", messages.get(2).getRole());
        assertEquals("assistant", messages.get(3).getRole());
        
        System.out.println("=== 测试完成 ===");
    }

    private void validateMessageFormat(List<ChatMessage> messages) throws Exception {
        System.out.println("\n=== 验证消息格式 ===");
        
        for (int i = 0; i < messages.size(); i++) {
            ChatMessage msg = messages.get(i);
            String msgJson = objectMapper.writeValueAsString(msg);
            System.out.println("消息 " + i + ": " + msgJson);
            
            // 验证基本字段
            assertNotNull(msg.getRole());
            
            switch (msg.getRole()) {
                case "user":
                    assertNotNull(msg.getContent());
                    assertNull(msg.getToolCalls());
                    assertNull(msg.getToolCallId());
                    break;
                    
                case "assistant":
                    // 助手消息可能有content和/或tool_calls
                    if (msg.getToolCalls() != null) {
                        // 有工具调用的助手消息
                        assertNull(msg.getToolCallId());
                    } else {
                        // 普通助手消息
                        assertNotNull(msg.getContent());
                        assertNull(msg.getToolCallId());
                    }
                    break;
                    
                case "tool":
                    assertNotNull(msg.getContent());
                    assertNotNull(msg.getToolCallId());
                    assertNull(msg.getToolCalls());
                    break;
                    
                default:
                    fail("未知的消息角色: " + msg.getRole());
            }
        }
        
        System.out.println("消息格式验证通过 ✅");
    }

    @Test
    void testToolCallIdMatching() throws Exception {
        System.out.println("\n=== 工具调用ID匹配测试 ===");
        
        // 创建带工具调用的助手消息
        String toolCallJson = """
            [{
                "id": "call_match_test",
                "type": "function",
                "function": {
                    "name": "file_search",
                    "arguments": "{\\"query\\": \\"test\\"}"
                }
            }]
            """;
        
        JsonNode toolCallsNode = objectMapper.readTree(toolCallJson);
        
        ChatMessage assistantMsg = ChatMessage.builder()
                .role("assistant")
                .content("执行搜索")
                .toolCalls(toolCallsNode)
                .build();
        
        // 创建对应的工具结果消息
        ChatMessage toolMsg = ChatMessage.builder()
                .role("tool")
                .content("搜索完成")
                .toolCallId("call_match_test")
                .build();
        
        // 验证ID匹配
        JsonNode toolCalls = (JsonNode) assistantMsg.getToolCalls();
        String expectedId = toolCalls.get(0).get("id").asText();
        String actualId = toolMsg.getToolCallId();
        
        assertEquals(expectedId, actualId);
        System.out.println("工具调用ID匹配验证通过: " + expectedId + " = " + actualId + " ✅");
    }

    @Test
    void testMessageSerializationCompatibility() throws Exception {
        System.out.println("\n=== 消息序列化兼容性测试 ===");
        
        // 测试各种消息类型的序列化
        List<ChatMessage> testMessages = List.of(
            // 用户消息
            ChatMessage.builder()
                .role("user")
                .content("Hello")
                .build(),
            
            // 普通助手消息
            ChatMessage.builder()
                .role("assistant")
                .content("Hi there!")
                .build(),
            
            // 带工具调用的助手消息
            ChatMessage.builder()
                .role("assistant")
                .content("Let me search for you")
                .toolCalls(objectMapper.readTree("[{\"id\":\"call_1\",\"type\":\"function\"}]"))
                .build(),
            
            // 工具结果消息
            ChatMessage.builder()
                .role("tool")
                .content("Search results")
                .toolCallId("call_1")
                .build()
        );
        
        for (ChatMessage msg : testMessages) {
            String json = objectMapper.writeValueAsString(msg);
            ChatMessage deserialized = objectMapper.readValue(json, ChatMessage.class);
            
            assertEquals(msg.getRole(), deserialized.getRole());
            assertEquals(msg.getContent(), deserialized.getContent());
            assertEquals(msg.getToolCallId(), deserialized.getToolCallId());
            
            System.out.println("序列化测试通过: " + msg.getRole() + " ✅");
        }
    }
}

package life.ljs.codeagent.tools.impl;

import life.ljs.codeagent.config.AgentConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FileSearchTool 测试类
 * 
 * <AUTHOR> carp
 * @version 1.0.0
 * @since 2025/8/22
 */
@SpringBootTest
@TestPropertySource(properties = {
    "agent.tools.file-search.max-results=10",
    "agent.tools.file-search.supported-extensions[0]=.java",
    "agent.tools.file-search.supported-extensions[1]=.txt",
    "agent.tools.file-search.supported-extensions[2]=.md",
    "agent.tools.file-search.exclude-dirs[0]=target",
    "agent.tools.file-search.exclude-dirs[1]=.git"
})
class FileSearchToolTest {

    private FileSearchTool fileSearchTool;
    private AgentConfig agentConfig;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() throws IOException {
        // 设置测试配置
        agentConfig = new AgentConfig();
        AgentConfig.ToolsConfig toolsConfig = new AgentConfig.ToolsConfig();
        AgentConfig.ToolsConfig.FileSearchConfig fileSearchConfig = new AgentConfig.ToolsConfig.FileSearchConfig();
        
        fileSearchConfig.setMaxResults(10);
        fileSearchConfig.setSupportedExtensions(Arrays.asList(".java", ".txt", ".md"));
        fileSearchConfig.setExcludeDirs(Arrays.asList("target", ".git"));
        
        toolsConfig.setFileSearch(fileSearchConfig);
        agentConfig.setTools(toolsConfig);
        
        fileSearchTool = new FileSearchTool(agentConfig);
        
        // 创建测试文件
        createTestFiles();
        
        // 设置工作目录为临时目录
        System.setProperty("user.dir", tempDir.toString());
    }

    private void createTestFiles() throws IOException {
        // 创建Java文件
        Path javaFile = tempDir.resolve("TestService.java");
        Files.write(javaFile, Arrays.asList(
            "package com.example;",
            "public class TestService {",
            "    public void searchMethod() {",
            "        System.out.println(\"Hello World\");",
            "    }",
            "}"
        ));

        // 创建文本文件
        Path txtFile = tempDir.resolve("readme.txt");
        Files.write(txtFile, Arrays.asList(
            "This is a test file",
            "It contains search keywords",
            "Hello World example"
        ));

        // 创建Markdown文件
        Path mdFile = tempDir.resolve("documentation.md");
        Files.write(mdFile, Arrays.asList(
            "# Documentation",
            "This file contains search content",
            "## Hello World Section"
        ));

        // 创建应该被排除的目录和文件
        Path targetDir = tempDir.resolve("target");
        Files.createDirectories(targetDir);
        Path excludedFile = targetDir.resolve("excluded.java");
        Files.write(excludedFile, Arrays.asList("This should be excluded"));
    }

    @Test
    void testGetName() {
        assertEquals("file_search", fileSearchTool.getName());
    }

    @Test
    void testGetDescription() {
        String description = fileSearchTool.getDescription();
        assertNotNull(description);
        assertTrue(description.contains("搜索"));
    }

    @Test
    void testGetParameters() {
        Map<String, Object> parameters = fileSearchTool.getParameters();
        assertNotNull(parameters);
        assertTrue(parameters.containsKey("type"));
        assertTrue(parameters.containsKey("properties"));
        assertTrue(parameters.containsKey("required"));
        
        @SuppressWarnings("unchecked")
        Map<String, Object> properties = (Map<String, Object>) parameters.get("properties");
        assertTrue(properties.containsKey("query"));
        assertTrue(properties.containsKey("searchType"));
        assertTrue(properties.containsKey("fileExtensions"));
    }

    @Test
    void testExecuteFilenameSearch() throws Exception {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "Test");
        arguments.put("searchType", "filename");
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        assertTrue(result.contains("TestService.java"));
        assertFalse(result.contains("Hello World")); // 不应该包含内容匹配
    }

    @Test
    void testExecuteContentSearch() throws Exception {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "Hello World");
        arguments.put("searchType", "content");
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        assertTrue(result.contains("内容匹配"));
        assertTrue(result.contains("Hello World"));
        assertFalse(result.contains("文件名匹配")); // 不应该包含文件名匹配
    }

    @Test
    void testExecuteBothSearch() throws Exception {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "test");
        arguments.put("searchType", "both");
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        // 应该同时包含文件名和内容匹配
        assertTrue(result.contains("TestService.java") || result.contains("test"));
    }

    @Test
    void testExecuteWithFileExtensions() throws Exception {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "Hello");
        arguments.put("searchType", "content");
        arguments.put("fileExtensions", Arrays.asList(".txt"));
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        assertTrue(result.contains("readme.txt"));
        assertFalse(result.contains("TestService.java")); // Java文件应该被过滤掉
    }

    @Test
    void testExecuteNoResults() throws Exception {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "nonexistent");
        arguments.put("searchType", "both");
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        assertTrue(result.contains("未找到"));
    }

    @Test
    void testExecuteDefaultSearchType() throws Exception {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "Hello");
        // 不指定searchType，应该默认为"both"
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        assertFalse(result.contains("未找到"));
    }

    @Test
    void testExecuteDefaultFileExtensions() throws Exception {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "Hello");
        arguments.put("searchType", "content");
        // 不指定fileExtensions，应该使用配置中的默认值
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        assertFalse(result.contains("未找到"));
    }

    @Test
    void testExecuteWithNullQuery() {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", null);
        
        assertThrows(Exception.class, () -> fileSearchTool.execute(arguments));
    }

    @Test
    void testExecuteWithEmptyArguments() {
        Map<String, Object> arguments = new HashMap<>();
        
        assertThrows(Exception.class, () -> fileSearchTool.execute(arguments));
    }

    @Test
    void testToDefinition() {
        var definition = fileSearchTool.toDefinition();
        
        assertNotNull(definition);
        assertEquals("function", definition.getType());
        assertNotNull(definition.getFunction());
        assertEquals("file_search", definition.getFunction().getName());
        assertNotNull(definition.getFunction().getDescription());
        assertNotNull(definition.getFunction().getParameters());
    }

    @Test
    void testCaseInsensitiveSearch() throws Exception {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "HELLO"); // 大写查询
        arguments.put("searchType", "content");
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        assertTrue(result.contains("Hello World")); // 应该能找到小写的内容
    }

    @Test
    void testExcludeDirectories() throws Exception {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "excluded");
        arguments.put("searchType", "content");
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        assertTrue(result.contains("未找到")); // target目录下的文件应该被排除
    }
}

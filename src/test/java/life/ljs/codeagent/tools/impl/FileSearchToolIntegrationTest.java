package life.ljs.codeagent.tools.impl;

import life.ljs.codeagent.config.AgentConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.TestPropertySource;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FileSearchTool 集成测试类
 * 测试在Spring容器环境下的完整功能
 * 
 * <AUTHOR> carp
 * @version 1.0.0
 * @since 2025/8/22
 */
@SpringBootTest
@TestPropertySource(properties = {
    "agent.tools.file-search.max-results=5",
    "agent.tools.file-search.supported-extensions[0]=.java",
    "agent.tools.file-search.supported-extensions[1]=.xml",
    "agent.tools.file-search.supported-extensions[2]=.yml",
    "agent.tools.file-search.exclude-dirs[0]=target",
    "agent.tools.file-search.exclude-dirs[1]=.git",
    "agent.tools.file-search.exclude-dirs[2]=.idea"
})
class FileSearchToolIntegrationTest {

    @Autowired
    private FileSearchTool fileSearchTool;

    @Autowired
    private AgentConfig agentConfig;

    @Autowired(required = false)
    private CacheManager cacheManager;

    @Test
    void testSpringContextLoads() {
        assertNotNull(fileSearchTool);
        assertNotNull(agentConfig);
    }

    @Test
    void testConfigurationBinding() {
        AgentConfig.ToolsConfig.FileSearchConfig config = agentConfig.getTools().getFileSearch();
        
        assertNotNull(config);
        assertEquals(5, config.getMaxResults());
        assertTrue(config.getSupportedExtensions().contains(".java"));
        assertTrue(config.getExcludeDirs().contains("target"));
    }

    @Test
    void testSearchInRealProject() throws Exception {
        // 在真实项目中搜索Java文件
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "FileSearchTool");
        arguments.put("searchType", "filename");
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        // 应该能找到FileSearchTool.java文件
        assertTrue(result.contains("FileSearchTool.java") || result.contains("未找到"));
    }

    @Test
    void testSearchProjectContent() throws Exception {
        // 搜索项目中的特定内容
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "SpringBootApplication");
        arguments.put("searchType", "content");
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        // 应该能在主应用类中找到@SpringBootApplication注解
        assertTrue(result.contains("SpringBootApplication") || result.contains("未找到"));
    }

    @Test
    void testSearchConfigurationFiles() throws Exception {
        // 搜索配置文件
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "agent");
        arguments.put("searchType", "content");
        arguments.put("fileExtensions", java.util.Arrays.asList(".yml", ".properties"));
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        // 应该能在application.yml中找到agent配置
        assertTrue(result.contains("agent") || result.contains("未找到"));
    }

    @Test
    void testCacheConfiguration() {
        if (cacheManager != null) {
            // 验证缓存管理器已配置
            assertNotNull(cacheManager.getCache("fileSearchCache"));
        }
    }

    @Test
    void testToolDefinitionGeneration() {
        var definition = fileSearchTool.toDefinition();
        
        assertNotNull(definition);
        assertEquals("function", definition.getType());
        
        var function = definition.getFunction();
        assertNotNull(function);
        assertEquals("file_search", function.getName());
        assertTrue(function.getDescription().contains("搜索"));
        
        @SuppressWarnings("unchecked")
        Map<String, Object> parameters = (Map<String, Object>) function.getParameters();
        assertNotNull(parameters);
        assertTrue(parameters.containsKey("properties"));
        assertTrue(parameters.containsKey("required"));
    }

    @Test
    void testSearchWithDifferentTypes() throws Exception {
        String[] searchTypes = {"filename", "content", "both"};
        
        for (String searchType : searchTypes) {
            Map<String, Object> arguments = new HashMap<>();
            arguments.put("query", "test");
            arguments.put("searchType", searchType);
            
            String result = fileSearchTool.execute(arguments);
            assertNotNull(result, "Search type " + searchType + " should return a result");
        }
    }

    @Test
    void testSearchPerformance() throws Exception {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "public");
        arguments.put("searchType", "content");
        
        long startTime = System.currentTimeMillis();
        String result = fileSearchTool.execute(arguments);
        long endTime = System.currentTimeMillis();
        
        assertNotNull(result);
        long executionTime = endTime - startTime;
        
        // 搜索应该在合理时间内完成（比如5秒内）
        assertTrue(executionTime < 5000, 
            "Search took too long: " + executionTime + "ms");
    }

    @Test
    void testSearchResultLimit() throws Exception {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "public"); // 常见关键词，可能有很多匹配
        arguments.put("searchType", "content");
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        
        // 计算结果中的行数，验证是否遵守了maxResults限制
        if (!result.contains("未找到")) {
            String[] lines = result.split("\n");
            // 结果应该是有限的，不会无限多
            assertTrue(lines.length < 100, "Too many results returned");
        }
    }

    @Test
    void testSearchWithSpecialCharacters() throws Exception {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "@"); // 搜索注解符号
        arguments.put("searchType", "content");
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        // 应该能处理特殊字符而不抛出异常
    }

    @Test
    void testSearchEmptyQuery() {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "");
        arguments.put("searchType", "both");
        
        // 空查询应该返回合理的结果或抛出异常
        assertDoesNotThrow(() -> {
            String result = fileSearchTool.execute(arguments);
            assertNotNull(result);
        });
    }

    @Test
    void testSearchNonExistentExtension() throws Exception {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("query", "test");
        arguments.put("searchType", "both");
        arguments.put("fileExtensions", java.util.Arrays.asList(".nonexistent"));
        
        String result = fileSearchTool.execute(arguments);
        
        assertNotNull(result);
        assertTrue(result.contains("未找到"));
    }
}

package life.ljs.codeagent.demo;

import life.ljs.codeagent.service.AgentService;
import life.ljs.codeagent.tools.impl.FileSearchTool;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Agent系统演示测试
 * 展示QwenLLMService与FileSearchTool的整体集成效果
 * 
 * <AUTHOR> carp
 * @version 1.0.0
 * @since 2025/8/22
 */
@SpringBootTest
@TestPropertySource(properties = {
    "agent.llm.api-key=test-key", // 使用测试密钥
    "agent.tools.file-search.max-results=10"
})
class AgentDemoTest {

    @Autowired
    private AgentService agentService;
    
    @Autowired
    private FileSearchTool fileSearchTool;

    @Test
    void demonstrateSystemArchitecture() {
        System.out.println("=== Agent系统架构演示 ===");
        
        // 1. 展示可用工具
        List<String> tools = agentService.getAvailableTools();
        System.out.println("可用工具: " + tools);
        
        Map<String, String> descriptions = agentService.getToolDescriptions();
        descriptions.forEach((name, desc) -> 
            System.out.println("  - " + name + ": " + desc));
        
        // 验证工具已正确注册
        assertTrue(tools.contains("file_search"));
        assertTrue(descriptions.containsKey("file_search"));
    }

    @Test
    void demonstrateFileSearchCapabilities() throws Exception {
        System.out.println("\n=== FileSearchTool功能演示 ===");
        
        // 1. 文件名搜索
        System.out.println("1. 文件名搜索 - 查找包含'Agent'的文件:");
        Map<String, Object> args1 = new HashMap<>();
        args1.put("query", "Agent");
        args1.put("searchType", "filename");
        
        String result1 = fileSearchTool.execute(args1);
        System.out.println(result1);
        assertNotNull(result1);
        
        // 2. 内容搜索
        System.out.println("\n2. 内容搜索 - 查找包含'SpringBootApplication'的内容:");
        Map<String, Object> args2 = new HashMap<>();
        args2.put("query", "SpringBootApplication");
        args2.put("searchType", "content");
        
        String result2 = fileSearchTool.execute(args2);
        System.out.println(result2);
        assertNotNull(result2);
        
        // 3. 混合搜索
        System.out.println("\n3. 混合搜索 - 查找包含'test'的文件和内容:");
        Map<String, Object> args3 = new HashMap<>();
        args3.put("query", "test");
        args3.put("searchType", "both");
        args3.put("fileExtensions", List.of(".java", ".yml"));
        
        String result3 = fileSearchTool.execute(args3);
        System.out.println(result3);
        assertNotNull(result3);
    }

    @Test
    void demonstrateAgentWorkflow() {
        System.out.println("\n=== Agent工作流程演示 ===");
        
        // 模拟用户请求
        String[] userQueries = {
            "帮我搜索项目中的Java文件",
            "查找包含'FileSearchTool'的文件",
            "搜索配置文件中的agent相关配置"
        };
        
        for (String query : userQueries) {
            System.out.println("\n用户请求: " + query);
            
            // 处理请求（会因为API密钥无效而失败，但展示了完整流程）
            String response = agentService.processMessage(query);
            System.out.println("Agent响应: " + response);
            
            assertNotNull(response);
            // 由于API密钥无效，响应应该包含错误信息
            assertTrue(response.contains("错误") || response.contains("失败"));
        }
    }

    @Test
    void demonstrateToolDefinitionStructure() {
        System.out.println("\n=== 工具定义结构演示 ===");
        
        var toolDef = fileSearchTool.toDefinition();
        System.out.println("工具类型: " + toolDef.getType());
        System.out.println("工具名称: " + toolDef.getFunction().getName());
        System.out.println("工具描述: " + toolDef.getFunction().getDescription());
        
        @SuppressWarnings("unchecked")
        Map<String, Object> params = (Map<String, Object>) toolDef.getFunction().getParameters();
        System.out.println("参数结构: " + params);
        
        // 验证工具定义的完整性
        assertEquals("function", toolDef.getType());
        assertEquals("file_search", toolDef.getFunction().getName());
        assertNotNull(toolDef.getFunction().getDescription());
        assertNotNull(toolDef.getFunction().getParameters());
    }

    @Test
    void demonstrateErrorHandling() {
        System.out.println("\n=== 错误处理演示 ===");
        
        // 1. 空消息处理
        String response1 = agentService.processMessage("");
        System.out.println("空消息响应: " + response1);
        assertNotNull(response1);
        
        // 2. null消息处理
        String response2 = agentService.processMessage(null);
        System.out.println("null消息响应: " + response2);
        assertNotNull(response2);
        
        // 3. 工具执行错误
        try {
            Map<String, Object> invalidArgs = new HashMap<>();
            // 缺少必需的query参数
            fileSearchTool.execute(invalidArgs);
        } catch (Exception e) {
            System.out.println("工具执行错误: " + e.getMessage());
            assertNotNull(e.getMessage());
        }
    }

    @Test
    void demonstrateSystemConfiguration() {
        System.out.println("\n=== 系统配置演示 ===");
        
        // 展示系统的配置信息
        System.out.println("这个Agent系统包含以下组件:");
        System.out.println("1. QwenLLMService - 负责与通义千问API通信");
        System.out.println("2. FileSearchTool - 提供文件搜索功能");
        System.out.println("3. AgentService - 协调LLM和工具调用");
        System.out.println("4. AgentController - 提供HTTP接口");
        System.out.println("5. AgentConfig - 管理系统配置");
        
        System.out.println("\n工作流程:");
        System.out.println("用户消息 -> AgentService -> QwenLLMService -> LLM API");
        System.out.println("LLM响应 -> 工具调用检测 -> FileSearchTool执行 -> 结果返回");
        System.out.println("工具结果 -> 再次调用LLM -> 最终响应 -> 用户");
        
        System.out.println("\n特性:");
        System.out.println("- 支持多种搜索类型（文件名、内容、混合）");
        System.out.println("- 可配置的文件扩展名过滤");
        System.out.println("- 目录排除功能");
        System.out.println("- 结果数量限制");
        System.out.println("- 缓存支持");
        System.out.println("- 错误处理");
        System.out.println("- Spring Boot集成");
    }

    @Test
    void demonstrateRealWorldUsage() throws Exception {
        System.out.println("\n=== 实际使用场景演示 ===");
        
        // 场景1: 代码审查 - 查找特定模式
        System.out.println("场景1: 代码审查 - 查找所有Service类");
        Map<String, Object> args1 = Map.of(
            "query", "Service",
            "searchType", "filename",
            "fileExtensions", List.of(".java")
        );
        String result1 = fileSearchTool.execute(args1);
        System.out.println(result1);
        
        // 场景2: 配置管理 - 查找配置项
        System.out.println("\n场景2: 配置管理 - 查找agent相关配置");
        Map<String, Object> args2 = Map.of(
            "query", "agent",
            "searchType", "content",
            "fileExtensions", List.of(".yml", ".properties")
        );
        String result2 = fileSearchTool.execute(args2);
        System.out.println(result2);
        
        // 场景3: 文档搜索 - 查找README文件
        System.out.println("\n场景3: 文档搜索 - 查找README文件");
        Map<String, Object> args3 = Map.of(
            "query", "README",
            "searchType", "filename"
        );
        String result3 = fileSearchTool.execute(args3);
        System.out.println(result3);
        
        // 验证所有场景都有响应
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotNull(result3);
    }
}

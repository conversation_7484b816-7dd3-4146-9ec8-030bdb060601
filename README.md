# CodeAgent - 智能代码助手

一个基于Spring Boot的智能代码助手系统，集成了通义千问LLM和文件搜索工具，能够理解自然语言指令并执行相应的代码操作。

## 🚀 系统架构

### 核心组件

1. **QwenLLMService** - 通义千问LLM服务
   - 负责与通义千问API通信
   - 支持工具调用功能
   - 可配置的模型参数

2. **FileSearchTool** - 文件搜索工具
   - 支持文件名搜索
   - 支持文件内容搜索
   - 支持混合搜索模式
   - 可配置的文件扩展名过滤
   - 目录排除功能

3. **AgentService** - 核心协调服务
   - 协调LLM和工具调用
   - 处理工具调用响应
   - 管理对话上下文

4. **AgentController** - HTTP接口
   - 提供RESTful API
   - 支持聊天接口
   - 工具信息查询

## 📋 功能特性

### 文件搜索功能
- ✅ 文件名搜索 - 根据文件名关键词查找文件
- ✅ 内容搜索 - 在文件内容中搜索关键词
- ✅ 混合搜索 - 同时搜索文件名和内容
- ✅ 扩展名过滤 - 支持指定文件类型
- ✅ 目录排除 - 自动排除target、.git等目录
- ✅ 结果限制 - 可配置最大搜索结果数
- ✅ 缓存支持 - 提高重复搜索性能

### LLM集成
- ✅ 通义千问API集成
- ✅ 工具调用支持
- ✅ 对话上下文管理
- ✅ 错误处理和重试机制

### 系统特性
- ✅ Spring Boot框架
- ✅ 配置文件管理
- ✅ 单元测试覆盖
- ✅ 集成测试验证
- ✅ RESTful API接口

## 🛠️ 技术栈

- **框架**: Spring Boot 3.5.4
- **语言**: Java 17+
- **构建工具**: Maven
- **LLM**: 通义千问 (Qwen)
- **HTTP客户端**: OkHttp
- **JSON处理**: Jackson
- **缓存**: Spring Cache + Caffeine
- **测试**: JUnit 5 + Spring Boot Test

## 📦 项目结构

```
src/
├── main/java/life/ljs/codeagent/
│   ├── config/
│   │   └── AgentConfig.java           # 系统配置
│   ├── controller/
│   │   └── AgentController.java       # HTTP控制器
│   ├── model/
│   │   ├── ChatMessage.java          # 聊天消息模型
│   │   └── ToolDefinition.java       # 工具定义模型
│   ├── service/
│   │   ├── AgentService.java         # 核心协调服务
│   │   └── QwenLLMService.java       # LLM服务
│   ├── tools/
│   │   ├── BaseTool.java             # 工具基础接口
│   │   └── impl/
│   │       └── FileSearchTool.java   # 文件搜索工具实现
│   └── CodeagentApplication.java     # 主应用类
├── test/java/life/ljs/codeagent/
│   ├── demo/
│   │   └── AgentDemoTest.java        # 系统演示测试
│   ├── service/
│   │   └── AgentServiceIntegrationTest.java  # 集成测试
│   └── tools/impl/
│       ├── FileSearchToolTest.java           # 单元测试
│       └── FileSearchToolIntegrationTest.java # 集成测试
└── resources/
    └── application.yml               # 配置文件
```

## ⚙️ 配置说明

### application.yml 配置

```yaml
agent:
  llm:
    provider: qwen
    api_key: ${QWEN_API_KEY}  # 通义千问API密钥
    base_url: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
    model: qwen-plus-2025-07-28
    max-tokens: 32768
    timeout: 30s
    temperature: 0.2

  tools:
    file-search:
      max-results: 20
      supported-extensions: [.java, .yml, .xml, .properties, .md, .sh, .txt]
      exclude-dirs:
        - target
        - .git
        - .idea
        - node_modules
```

## 🚀 快速开始

### 1. 环境要求
- Java 17+
- Maven 3.6+
- 通义千问API密钥

### 2. 配置API密钥
```bash
export QWEN_API_KEY=your_api_key_here
```

### 3. 运行应用
```bash
mvn spring-boot:run
```

### 4. 测试API
```bash
# 健康检查
curl http://localhost:8080/api/agent/health

# 获取工具列表
curl http://localhost:8080/api/agent/tools

# 发送聊天消息
curl -X POST http://localhost:8080/api/agent/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "帮我搜索项目中的Java文件"}'
```

## 🧪 测试

### 运行所有测试
```bash
mvn test
```

### 运行特定测试
```bash
# 文件搜索工具测试
mvn test -Dtest=FileSearchToolTest

# 集成测试
mvn test -Dtest=AgentServiceIntegrationTest

# 演示测试
mvn test -Dtest=AgentDemoTest
```

## 📊 工作流程

```
用户输入 -> AgentController -> AgentService -> QwenLLMService -> 通义千问API
                                      ↓
                               检测工具调用需求
                                      ↓
                              FileSearchTool执行
                                      ↓
                               工具结果返回LLM
                                      ↓
                              生成最终响应给用户
```

## 🔧 扩展开发

### 添加新工具

1. 实现 `BaseTool` 接口
2. 添加 `@Component` 注解
3. 实现必要的方法：
   - `getName()` - 工具名称
   - `getDescription()` - 工具描述
   - `getParameters()` - 参数定义
   - `execute()` - 执行逻辑

### 示例工具实现
```java
@Component
public class MyTool implements BaseTool {
    @Override
    public String getName() {
        return "my_tool";
    }
    
    @Override
    public String getDescription() {
        return "我的自定义工具";
    }
    
    @Override
    public Map<String, Object> getParameters() {
        // 定义工具参数
    }
    
    @Override
    public String execute(Map<String, Object> arguments) throws Exception {
        // 实现工具逻辑
    }
}
```

## 📈 性能优化

- ✅ 文件搜索结果缓存
- ✅ 搜索结果数量限制
- ✅ 目录排除优化
- ✅ 连接池复用

## 🔒 安全考虑

- API密钥通过环境变量配置
- 文件访问限制在项目目录内
- 搜索结果数量限制防止资源耗尽

## 📝 更新日志

### v1.0.0 (2025-08-22)
- ✅ 初始版本发布
- ✅ 集成通义千问LLM
- ✅ 实现文件搜索工具
- ✅ 完整的测试覆盖
- ✅ RESTful API接口

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 👥 作者

- little carp - 初始开发

---

**注意**: 使用前请确保已正确配置通义千问API密钥。
